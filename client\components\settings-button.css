/* Settings button styles */
.settings-button {
  width: 28px; /* Match play button height */
  height: 28px; /* Match play button height */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background);
  border: 1px solid var(--border);
  border-radius: 4px;
  cursor: pointer;
  box-shadow: 0 1px 5px var(--shadow);
  transition: background-color 0.2s;
  padding: 0;
  box-sizing: border-box;
}

.settings-button:hover {
  background-color: var(--background-hover);
}

/* Action buttons in modal - styled like Play button */
.settings-button-action {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  height: 28px; /* Match play button height */
  text-align: center;
}

.settings-button-action.secondary {
  background-color: var(--background-hover);
  color: var(--text);
  border: 1px solid var(--border);
}

.settings-button-action.secondary:hover {
  background-color: var(--primary-light);
}

.settings-button-action.primary {
  background-color: var(--primary);
  color: var(--white);
}

.settings-button-action.primary:hover {
  background-color: var(--primary-hover);
}

/* Modal styles */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  font-family: "Helvetica Neue", Arial, Helvetica, sans-serif;
}

.settings-modal-content {
  background-color: var(--background);
  border-radius: 4px;
  box-shadow: 0 2px 10px var(--shadow-heavy);
  width: 100%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
}

.settings-modal-header {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.settings-modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.settings-modal-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: var(--text-muted);
}

.settings-modal-body {
  padding: 15px;
}

.settings-modal-footer {
  padding: 15px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.settings-form-group {
  margin-bottom: 15px;
}

.settings-form-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
  font-weight: bold;
  color: var(--text-secondary);
}

.settings-form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border);
  border-radius: 4px;
  font-size: 12px;
  box-sizing: border-box;
  text-align: left;
  line-height: 16px; /* Adjust line height for better vertical centering */
}
