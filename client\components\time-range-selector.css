/* Leaflet-inspired timeline styles */
.time-range-selector {
  font-family: "Helvetica Neue", Arial, Helvetica, sans-serif;
  font-size: 12px;
  line-height: 1.4;
  color: var(--text);
  width: 70%;
  margin: 0 auto;
  padding: 10px 0 20px; /* Increased bottom padding to move timeline up */
  box-sizing: border-box;
  pointer-events: auto;
}

/* Controls container for width control */
.time-controls-container {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: 10px;
}

/* Controls styles */
.time-controls {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
  justify-content: flex-end;
}

/* Button styles */
.play-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary);
  color: var(--white);
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  height: 28px; /* Fixed height to match other controls */
  text-align: center;
}

.play-button:hover {
  background-color: var(--primary-hover);
}

.play-button.playing {
  background-color: var(--secondary);
}

.play-button.playing:hover {
  background-color: var(--secondary-hover);
}

/* Input styles */
.datetime-picker input {
  padding: 6px 8px;
  border: 1px solid var(--border);
  border-radius: 4px;
  font-size: 12px;
  height: 28px; /* Fixed height to match other controls */
  box-sizing: border-box;
  text-align: left;
}

/* Lookback selector wrapper for custom dropdown icon */
.lookback-wrapper {
  position: relative;
  display: inline-block;
}

.select-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.lookback-selector {
  padding: 6px 8px;
  border: 1px solid var(--border);
  border-radius: 4px;
  background-color: var(--background);
  font-size: 12px;
  height: 28px; /* Fixed height to match other controls */
  text-align: left;
  text-align-last: left; /* Left align text in dropdown */
  vertical-align: middle; /* Ensure text is vertically centered */
  line-height: 16px; /* Adjust line height for better vertical centering */
  appearance: none; /* Remove default arrow */
  padding-right: 24px; /* Make room for custom arrow */
}

/* Timeline container for centering */
.timeline-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Timeline styles - increased height to accommodate date labels */
.timeline {
  position: relative;
  height: 48px; /* Increased height to fit date labels inside */
  border: 1px solid var(--border);
  border-radius: 4px;
  background-color: rgba(248, 248, 248, 0.8); /* Semi-transparent background */
  cursor: pointer;
  box-shadow: 0 1px 5px var(--shadow);
  padding: 5px 0; /* Reduced padding */
  box-sizing: border-box; /* Include padding in height */
  width: 100%; /* Set to 70% width */
  touch-action: none; /* Prevent browser handling of touch events */
}

.timeline.dragging {
  cursor: grabbing;
}

/* Time interval and tick mark styles */
.time-interval {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 0; /* Zero width as we're just positioning a point */
}

.tick-container {
  position: absolute;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center; /* Center vertically */
  align-items: center;
}

.tick-mark {
  width: 1px;
  background-color: var(--text-muted);
  flex-grow: 0;
}

.tick-mark.hour {
  height: 16px; /* Reduced height */
}

.tick-mark:not(.hour) {
  height: 10px; /* Reduced height */
}

/* Date label inside the timeline for midnight */
.date-label {
  position: absolute;
  top: 2px; /* Moved closer to top edge, away from ticks */
  left: 0;
  transform: translateX(-50%); /* Center the label on the tick */
  font-size: 10px;
  color: var(--text-muted);
  font-weight: normal; /* Removed bold */
  text-align: center;
  white-space: nowrap;
  z-index: 5;
}

/* Time label below the tick */
.time-label {
  position: absolute;
  bottom: 2px; /* Moved closer to bottom edge, away from ticks */
  left: 0;
  transform: translateX(-50%); /* Center the label on the tick */
  font-size: 10px;
  color: var(--text-muted);
  text-align: center;
  white-space: nowrap;
  z-index: 5; /* Ensure labels are above other elements */
}

/* Timeline overlay for darkening effect */
.timeline-overlay {
  position: absolute;
  top: 0; /* Cover entire timeline height */
  bottom: 0; /* Cover entire timeline height */
  background-color: var(--shadow);
  pointer-events: none;
  z-index: 5;
  border-radius: 4px;
}

/* Marker styles */
.time-marker {
  position: absolute;
  top: 5px; /* Adjusted for reduced padding */
  bottom: 5px; /* Adjusted for reduced padding */
  width: 2px;
  background-color: var(--secondary);
  border-radius: 4px;
  transform: translateX(-50%);
  transition: background-color 0.2s;
  cursor: grab;
  z-index: 10;
  touch-action: none; /* Prevent browser handling of touch events */
}

.time-marker.dragging {
  cursor: grabbing;
}

.time-marker.playing {
  background-color: var(--primary);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.marker-label {
  position: absolute;
  top: -30px; /* Moved higher above the marker */
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  background-color: var(--primary);
  color: var(--white);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: normal;
  box-shadow: 0 1px 3px var(--shadow);
  z-index: 20; /* Ensure marker label is on top */
}

.time-marker.playing .marker-label {
  background-color: var(--primary);
}

.marker-pointer {
  position: absolute;
  top: -5px; /* Position at the top of the marker */
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid var(--secondary);
}

.time-marker.playing .marker-pointer {
  border-top-color: var(--primary);
}

/* Mobile styles */
@media (max-width: 768px) {
  .time-range-selector.mobile {
    width: 90%; /* Wider on mobile to match timeline width */
    padding: 5px 0 10px;
    /* No background color */
  }

  .time-controls {
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
  }

  .datetime-picker input {
    width: 140px; /* Ensure it fits on small screens */
  }

  .lookback-selector {
    width: auto;
  }

  .marker-label {
    top: -25px;
    font-size: 10px;
    padding: 1px 4px;
  }
}
